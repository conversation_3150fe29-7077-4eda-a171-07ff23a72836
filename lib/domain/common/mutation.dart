import 'package:bitacora/domain/common/model.dart';
import 'package:bitacora/domain/common/mutation_type.dart';
import 'package:bitacora/domain/common/value_object/common.dart';

class Mutation<T extends Model> {
  final MutationType type;
  final LocalId? id;
  final T? model;

  Mutation({required this.type, this.id, this.model});

  Mutation<T> copyWith({
    MutationType? type,
    LocalId? id,
    T? model,
  }) {
    return Mutation<T>(
      type: type ?? this.type,
      id: id ?? this.id,
      model: model ?? this.model,
    );
  }
}
