import 'dart:async';

import 'package:bitacora/application/sync/machine/steps/download/collection/sync_collection_downloader.dart';
import 'package:bitacora/application/sync/machine/sync_machine_step.dart';
import 'package:bitacora/application/sync/sync_trigger.dart';
import 'package:bitacora/domain/sync_metadata/sync_metadata.dart';
import 'package:bitacora/domain/sync_metadata/value/sync_metadata_last_sync_time.dart';
import 'package:bitacora/util/date_utils.dart';
import 'package:bitacora/util/logger/logger.dart';

class SyncMachineStepCollectionDownload extends SyncMachineStep {
  @override
  final String debugName;
  final List<SyncCollectionDownloader> downloaders;

  SyncMachineStepCollectionDownload(
      super.params, this.debugName, this.downloaders);

  @override
  Set<SyncTriggerSource> get triggerResetSources {
    return <SyncTriggerSource>{
      ...downloaders
          .map((downloader) => downloader.triggerResetSources)
          .expand((source) => source)
    };
  }

  @override
  Future<void> performSync() async {
    var page = 0;
    var syncMetadataList =
        await Future.wait(downloaders.map((e) => e.getSyncMetadata()));

    do {
      logger.i(wrapSyncLog('download page[${++page}]'));

      final nextPostParams = _buildPostParams(syncMetadataList);
      final response = await apiHelper.post('sync', data: nextPostParams);

      final lastSyncTime = SyncMetadataLastSyncTime(
          getDateTimeFromApiEpoch(response.data['sync_time']));


      final downloadFutures = downloaders.map((e) async {
        final data = response.data[e.collectionType.value.apiKey];
        if (data != null) {
          await e.download(data, lastSyncTime);
        }
        return e.getSyncMetadata();
      });

      final nextSyncMetadataList = await Future.wait(downloadFutures);

      _maybeShortCircuitInfiniteLoop(syncMetadataList, nextSyncMetadataList);

      syncMetadataList = nextSyncMetadataList;
    } while (_hasMoreData(syncMetadataList) && !isCanceled);

    await Future.wait(downloaders.map((e) async {
      await e.resolvePendingRelations();
    }));
  }

  bool _hasMoreData(List<SyncMetadata> nextSyncMetadataList) {
    for (final syncMetadata in nextSyncMetadataList) {
      if (syncMetadata.nextPageToken?.value != null) {
        return true;
      }
    }

    return false;
  }

  void _maybeShortCircuitInfiniteLoop(
    List<SyncMetadata> syncMetadataList,
    List<SyncMetadata> nextSyncMetadataList,
  ) {
    for (var i = 0; i < syncMetadataList.length; i++) {
      final currentToken = syncMetadataList[i].nextPageToken?.value;
      final nextToken = nextSyncMetadataList[i].nextPageToken?.value;
      if (currentToken != null &&
          nextToken != null &&
          currentToken == nextToken) {
        throw 'Infinite loop detected '
            '${syncMetadataList[i].collectionType?.displayValue}: $nextToken';
      }
    }
  }

  Map<String, dynamic> _buildPostParams(
    List<SyncMetadata> nextSyncMetadataList,
  ) {
    final nextPostParams = <String, dynamic>{
      'organization_id': organization.remoteId!.apiValue,
    };
    final syncItems = <String, dynamic>{};

    for (final nextMetadata in nextSyncMetadataList) {
      final collectionSyncMetadata = {};
      if (nextMetadata.lastSyncTime?.value != null) {
        collectionSyncMetadata['last_sync_time'] = microSecondsToApi(
          nextMetadata.lastSyncTime!.value!.microsecondsSinceEpoch,
        );
      }

      if (nextMetadata.nextPageToken?.value != null) {
        collectionSyncMetadata['next_page_token'] =
            nextMetadata.nextPageToken!.value;
      }

      syncItems[nextMetadata.collectionType!.value.apiKey] =
          collectionSyncMetadata;
    }

    nextPostParams['sync_items'] = syncItems;
    return nextPostParams;
  }
}
