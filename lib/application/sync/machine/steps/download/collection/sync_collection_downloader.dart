import 'dart:async';

import 'package:bitacora/application/sync/machine/steps/download/collection/sync_metadata_repository_query.dart';
import 'package:bitacora/application/sync/machine/sync_machine_params.dart';
import 'package:bitacora/application/sync/pending_relations/sync_pending_relations_service.dart';
import 'package:bitacora/application/sync/sync_trigger.dart';
import 'package:bitacora/domain/common/query_scope.dart';
import 'package:bitacora/domain/common/repository.dart';
import 'package:bitacora/domain/common/translator.dart';
import 'package:bitacora/domain/common/value_object/common.dart';
import 'package:bitacora/domain/organization/organization.dart';
import 'package:bitacora/domain/sync_metadata/sync_metadata.dart';
import 'package:bitacora/domain/sync_metadata/value/sync_metadata_collection_type.dart';
import 'package:bitacora/domain/sync_metadata/value/sync_metadata_last_sync_time.dart';
import 'package:bitacora/domain/sync_metadata/value/sync_metadata_next_page_token.dart';
import 'package:bitacora/util/logger/logger.dart';
import 'package:flutter/widgets.dart';

abstract class SyncCollectionDownloader {
  final SyncMachineParams params;

  SyncCollectionDownloader(this.params);

  Repository get db => params.db;

  Translator get apiTranslator => params.apiTranslator;

  Organization get organization => params.organization;

  SyncPendingRelationsService get pendingRelationsService =>
      SyncPendingRelationsService(db: db, organization: organization);

  @protected
  int get syncVersion => 1;

  DateTime? get startResyncTime => null;

  SyncMetadataCollectionType get collectionType;

  Set<SyncTriggerSource> get triggerResetSources => <SyncTriggerSource>{};

  @protected
  Future<LocalId?> translateAndSave(Map<String, dynamic> map);

  @protected
  Future<void> deleteArchived(RemoteId remoteId);

  @protected
  Future<List<LocalId?>> translateAndSaveBatch(
          List<Map<String, dynamic>> maps) async =>
      throw UnimplementedError();

  @protected
  bool get supportsBatchOperations => false;

  Future<SyncMetadata> getSyncMetadata() async {
    final syncMetadata = await db.query(
          SyncMetadataRepositoryQuery(
            syncMetadataCollectionType: collectionType,
          ),
          context: db.context(queryScope: QueryScope(orgId: organization.id!)),
        ) ??
        SyncMetadata(
          collectionType: collectionType,
          syncVersion: SyncMetadataSyncVersion(1),
        );

    if (syncVersion == syncMetadata.syncVersion!.value) {
      return syncMetadata;
    }

    await db.syncMetadata.save(
      db.context(),
      SyncMetadata(
        organization: Organization(id: organization.id!),
        collectionType: collectionType,
        syncVersion: SyncMetadataSyncVersion(syncVersion),
      ),
    );

    return SyncMetadata(
      collectionType: collectionType,
      lastSyncTime: SyncMetadataLastSyncTime(startResyncTime),
      nextPageToken: SyncMetadataNextPageToken(null),
      syncVersion: SyncMetadataSyncVersion(syncVersion),
      organization: params.organization,
    );
  }

  Future<void> download(
    Map<String, dynamic> data,
    SyncMetadataLastSyncTime lastSyncTime,
  ) async {
    final archivedResourcesData = data['archived_items'];
    if (archivedResourcesData != null) {
      await _processArchivedItems(archivedResourcesData);
    }

    final itemsData = data['items'] as List;
    if (itemsData.isNotEmpty) {
      await _processItems(itemsData);
    }

    final nextSyncNextPageToken =
        SyncMetadataNextPageToken(data['next_page_token']);
    await db.syncMetadata.save(
      db.context(),
      SyncMetadata(
        organization: Organization(id: organization.id!),
        collectionType: collectionType,
        nextPageToken: nextSyncNextPageToken,
        lastSyncTime: lastSyncTime,
        syncVersion: SyncMetadataSyncVersion(syncVersion),
      ),
    );
  }

  Future<void> _processArchivedItems(List archivedResourcesData) async {
    const batchSize = 10;

    for (int i = 0; i < archivedResourcesData.length; i += batchSize) {
      final batch = archivedResourcesData.skip(i).take(batchSize);

      await Future.wait(
        batch.map<Future<void>>((id) async {
          try {
            logger
                .i('sync:download:${collectionType.value.apiKey} archive $id');
            await deleteArchived(RemoteId(id));
          } catch (e, s) {
            logger.f(_wrapSyncLog('Failed to delete archived $id: $e\n$s'));
          }
        }),
      );
    }
  }

  Future<void> _processItems(List itemsData) async {
    if (supportsBatchOperations) {
      await _processItemsInBatches(itemsData);
    } else {
      await _processItemsIndividual(itemsData);
    }
  }

  Future<void> _processItemsInBatches(List itemsData) async {
    const batchSize = 50;

    for (int i = 0; i < itemsData.length; i += batchSize) {
      final batch = itemsData.skip(i).take(batchSize).toList();

      try {
        final start = DateTime.now();
        final ids = await translateAndSaveBatch(
          batch.cast<Map<String, dynamic>>(),
        );
        logger.d(_wrapSyncLog(
          'Batch saved ${ids.length} items '
          '[+${DateTime.now().difference(start).inMilliseconds}ms]',
        ));
      } catch (e, s) {
        logger.f(_wrapSyncLog('Failed to save batch: $e\n$s'));
        await _processItemsIndividual(batch);
      }
    }
  }

  Future<void> _processItemsIndividual(List itemsData) async {
    for (final itemData in itemsData) {
      try {
        final start = DateTime.now();
        final id = await translateAndSave(itemData);
        logger.d(_wrapSyncLog(
          'Saved [$id] '
          '[+${DateTime.now().difference(start).inMilliseconds}ms]',
        ));
      } catch (e, s) {
        logger.f(_wrapSyncLog('Failed to save $e\n$s'));
      }
    }
  }

  Future<void> resolvePendingRelations() async {}

  String _wrapSyncLog(String s) =>
      'sync:download:${collectionType.value.apiKey}${params.durationTracker} $s';
}
