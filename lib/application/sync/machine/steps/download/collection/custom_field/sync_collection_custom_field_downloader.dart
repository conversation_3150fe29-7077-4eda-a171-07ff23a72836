import 'package:bitacora/application/sync/machine/steps/download/collection/custom_field/custom_field_by_remote_id_repository_query.dart';
import 'package:bitacora/application/sync/machine/steps/download/collection/sync_collection_downloader.dart';
import 'package:bitacora/domain/common/repository_query_context.dart';
import 'package:bitacora/domain/common/value_object/local_id.dart';
import 'package:bitacora/domain/common/value_object/remote_id.dart';
import 'package:bitacora/domain/custom_field/custom_field.dart';
import 'package:bitacora/domain/sync_metadata/value/sync_metadata_collection_type.dart';
import 'package:bitacora/domain/sync_pending_relation/value/sync_pending_relation_collection_type.dart';
import 'package:bitacora/util/logger/logger.dart';

class SyncCollectionCustomFieldDownloader extends SyncCollectionDownloader {
  SyncCollectionCustomFieldDownloader(super.params);

  @override
  SyncMetadataCollectionType get collectionType =>
      SyncMetadataCollectionType.customField;

  @override
  bool get supportsBatchOperations => true;

  @override
  Future<List<LocalId?>> translateAndSaveBatch(
    List<Map<String, dynamic>> maps,
  ) async {
    return db.transaction<List<LocalId?>>((dbContext) async {
      final results = <LocalId?>[];
      final pendingRelations = <Map<String, dynamic>>[];

      for (final map in maps) {
        final remoteId = RemoteId(map['id']);
        final parentRemoteId = RemoteId(map['parent_id']);

        if (remoteId.value == null) {
          results.add(null);
          continue;
        }

        // Add organization_id to map
        map['organization_id'] = params.organization.id!.dbValue;

        // If no parent needed, save complete custom field
        if (parentRemoteId.value == null) {
          final id = await _saveCompleteCustomField(dbContext, map);
          results.add(id);
          continue;
        }

        // Check if parent exists
        final parentField = await db.query(
          CustomFieldByRemoteIdRepositoryQuery(parentRemoteId),
          context: dbContext,
        );

        if (parentField?.id != null) {
          // Parent exists, save complete custom field
          final id = await _saveCompleteCustomField(dbContext, map);
          results.add(id);
        } else {
          // Parent doesn't exist, save without parent and create pending relation
          final customFieldWithoutParent =
              apiTranslator.customField.fromMap(map..remove('parent_id'));
          final customFieldId =
              await db.customField.save(dbContext, customFieldWithoutParent);

          if (customFieldId != null) {
            pendingRelations.add({
              'parentRemoteId': parentRemoteId,
              'childLocalId': customFieldId,
              'allowedValues': map['allowed_values'],
            });
          }

          results.add(customFieldId);
        }
      }

      // Second pass: create pending relations for items that need them
      for (final relation in pendingRelations) {
        await pendingRelationsService.addPendingRelation(
          dbContext: dbContext,
          collectionType: const SyncPendingRelationCollectionType(
            SyncPendingRelationCollectionTypeValue.customField,
          ),
          parentRemoteId: relation['parentRemoteId'],
          childLocalId: relation['childLocalId'],
          pendingData: {'allowed_values': relation['allowedValues']},
        );
      }

      return results;
    });
  }

  @override
  Future<LocalId?> translateAndSave(Map<String, dynamic> map) async {
    final remoteId = RemoteId(map['id']);
    final parentRemoteId = RemoteId(map['parent_id']);

    if (remoteId.value == null) {
      return null;
    }

    return db.transaction<LocalId?>((dbContext) async {
      LocalId? parentLocalId;

      if (parentRemoteId.value != null) {
        final parentField = await db.query(
          CustomFieldByRemoteIdRepositoryQuery(parentRemoteId),
          context: dbContext,
        );
        parentLocalId = parentField?.id;
      }

      map['organization_id'] = params.organization.id!.dbValue;

      if (parentRemoteId.value == null) {
        return _saveCompleteCustomField(dbContext, map);
      }

      if (parentRemoteId.value != null && parentLocalId != null) {
        return _saveCompleteCustomField(dbContext, map);
      }

      final customFieldWithoutParent =
          apiTranslator.customField.fromMap(map..remove('parent_id'));
      final customFieldId =
          await db.customField.save(dbContext, customFieldWithoutParent);

      if (customFieldId == null) {
        return null;
      }

      await pendingRelationsService.addPendingRelation(
        dbContext: dbContext,
        collectionType: const SyncPendingRelationCollectionType(
          SyncPendingRelationCollectionTypeValue.customField,
        ),
        parentRemoteId: parentRemoteId,
        childLocalId: customFieldId,
        pendingData: {'allowed_values': map['allowed_values']},
      );

      return customFieldId;
    });
  }

  Future<LocalId?> _saveCompleteCustomField(
      RepositoryQueryContext dbContext, Map<String, dynamic> map) async {
    final customField = apiTranslator.customField.fromMap(map);
    final customFieldId = await db.customField.save(dbContext, customField);

    await _saveAllowedValues(dbContext, map['allowed_values'], customFieldId);

    return customFieldId;
  }

  Future<void> _saveAllowedValues(
    RepositoryQueryContext dbContext,
    List<dynamic> allowedValuesData,
    LocalId? customFieldId,
  ) async {
    await Future.wait(allowedValuesData.map((e) {
      e['custom_field_id'] = customFieldId!.dbValue;
      final allowedValue = apiTranslator.customFieldAllowedValue.fromMap(e);
      return db.customFieldAllowedValue.save(dbContext, allowedValue);
    }));
  }

  @override
  Future<void> deleteArchived(RemoteId remoteId) async {
    await db.transaction((context) async {
      final customField = await db.query(
          CustomFieldByRemoteIdRepositoryQuery(remoteId),
          context: context);
      if (customField != null) {
        await db.customField.delete(context, customField.id!);
      }
    });
  }

  @override
  Future<void> resolvePendingRelations() async {
    await pendingRelationsService.resolvePendingRelations<CustomField>(
      collectionType: const SyncPendingRelationCollectionType(
        SyncPendingRelationCollectionTypeValue.customField,
      ),
      resolver: (dbContext, relation) async {
        final parent = await db.query(
          CustomFieldByRemoteIdRepositoryQuery(relation.parentRemoteId!),
        );

        if (parent?.id == null) {
          return false;
        }

        final childToUpdate = await db.customField.find(
          dbContext.copyWith(
            fields: db.customField.fieldsBuilder.build(),
          ),
          relation.childLocalId!,
        );

        if (childToUpdate == null) {
          return false;
        }

        await db.customField.save(
          dbContext,
          childToUpdate.copyWith(parent: CustomField(id: parent!.id!)),
        );

        final pendingData = relation.pendingData?.dataMap;
        if (pendingData != null && pendingData['allowed_values'] != null) {
          await _saveAllowedValues(
            dbContext,
            pendingData['allowed_values'],
            relation.childLocalId!,
          );
          logger.i(
            'sync:download:${collectionType.value.apiKey}${params.durationTracker} '
            'Synced allowed_values for ${relation.childLocalId!.value} after parent resolution',
          );
        }

        return true;
      },
    );
  }
}
