import 'package:bitacora/application/sync/machine/steps/download/collection/resource/resource_by_remote_id_repository_query.dart';
import 'package:bitacora/application/sync/machine/steps/download/collection/sync_collection_downloader.dart';
import 'package:bitacora/domain/common/value_object/local_id.dart';
import 'package:bitacora/domain/common/value_object/remote_id.dart';
import 'package:bitacora/domain/sync_metadata/value/sync_metadata_collection_type.dart';

class SyncCollectionResourceDownloader extends SyncCollectionDownloader {
  SyncCollectionResourceDownloader(super.params);

  @override
  SyncMetadataCollectionType get collectionType =>
      SyncMetadataCollectionType.resource;

  @override
  bool get supportsBatchOperations => true;

  @override
  Future<LocalId?> translateAndSave(Map<String, dynamic> map) async {
    final resource = apiTranslator.resource.fromMap(map);
    return db.resource.save(db.context(), resource);
  }

  @override
  Future<List<LocalId?>> translateAndSaveBatch(List<Map<String, dynamic>> maps) async {
    final resources = maps.map((map) => apiTranslator.resource.fromMap(map)).toList();
    return db.resource.saveBatch(db.context(), resources);
  }

  @override
  Future<void> deleteArchived(RemoteId remoteId) async {
    final resource =
        await db.query(ResourceByRemoteIdRepositoryQuery(remoteId));
    if (resource != null) {
      await db.resource.delete(db.context(), resource.id!);
    }
  }
}
