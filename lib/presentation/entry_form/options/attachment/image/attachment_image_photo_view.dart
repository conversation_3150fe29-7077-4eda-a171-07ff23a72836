import 'package:bitacora/domain/attachment/attachment.dart';
import 'package:bitacora/presentation/entry_form/options/attachment/attachment_gallery_page_item_controller.dart';
import 'package:bitacora/presentation/entry_form/options/attachment/image/doodle/doodle_canvas.dart';
import 'package:bitacora/util/file_system.dart';
import 'package:flutter/material.dart';
import 'package:photo_view/photo_view.dart';

class AttachmentImagePhotoView extends StatefulWidget {
  final AttachmentGalleryPageItemController controller;
  final Attachment attachment;
  final String absolutePath;
  final ImageErrorWidgetBuilder errorBuilder;
  final ValueNotifier<bool> isShowingDoodleEditor;

  const AttachmentImagePhotoView({
    super.key,
    required this.controller,
    required this.attachment,
    required this.absolutePath,
    required this.errorBuilder,
    required this.isShowingDoodleEditor,
  });

  @override
  State<AttachmentImagePhotoView> createState() =>
      _AttachmentImagePhotoViewState();
}

class _AttachmentImagePhotoViewState extends State<AttachmentImagePhotoView> {
  final PhotoViewController _photoViewController = PhotoViewController();
  File? _imageFile;
  Size? _imageSize;

  @override
  void initState() {
    super.initState();
    _loadImage();
  }

  @override
  void dispose() {
    _photoViewController.dispose();
    super.dispose();
  }

  void _loadImage() async {
    _imageFile = FileSystemInjector.get().file(widget.absolutePath);
    final decodedImage =
        await decodeImageFromList(_imageFile!.readAsBytesSync());
    _imageSize =
        Size(decodedImage.width.toDouble(), decodedImage.height.toDouble());
    if (mounted) {
      setState(() {});
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_imageFile == null || _imageSize == null) {
      return const SizedBox();
    }
    return Stack(
      children: [
        PhotoViewGestureDetectorScope(
          axis: Axis.horizontal,
          child: PhotoView(
            controller: _photoViewController,
            backgroundDecoration:
                const BoxDecoration(color: Colors.transparent),
            imageProvider: FileImage(_imageFile!),
            errorBuilder: widget.errorBuilder,
          ),
        ),
        DoodleCanvas(
          controller: widget.controller,
          photoViewController: _photoViewController,
          isShowingDoodleEditor: widget.isShowingDoodleEditor,
          imageSize: _imageSize!,
        ),
      ],
    );
  }
}
